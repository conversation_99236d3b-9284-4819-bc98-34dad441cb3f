//! Kanna<PERSON><PERSON><PERSON> to Double Sphere Conversion Example
//!
//! This example demonstrates the conversion from Kannala-Brandt camera model
//! to Double Sphere camera model using the fisheye-tools library.
//! This is specifically designed to debug and compare with C++ implementation.
//!
//! Usage:
//! ```bash
//! cargo run --example kb_to_ds_conversion
//! ```

use fisheye_tools::camera::{CameraModel, DoubleSphereModel, KannalaBrandtModel};
use fisheye_tools::optimization::{DoubleSphereOptimizationCost, Optimizer};
use fisheye_tools::geometry;

fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Initialize simple console logger
    env_logger::init();

    println!("=== Kannala-Brandt to Double Sphere Conversion ===");
    
    // Load Kannala-Brandt model from YAML
    let kb_model = KannalaBrandtModel::load_from_yaml("samples/kannala_brandt.yaml")?;
    
    println!("\nInput Kannala-Brandt Model:");
    println!("  Intrinsics: fx={:.6}, fy={:.6}, cx={:.6}, cy={:.6}", 
             kb_model.intrinsics.fx, kb_model.intrinsics.fy, 
             kb_model.intrinsics.cx, kb_model.intrinsics.cy);
    println!("  Resolution: {}x{}", kb_model.resolution.width, kb_model.resolution.height);
    println!("  Distortion: k1={:.10}, k2={:.10}, k3={:.10}, k4={:.10}", 
             kb_model.distortions[0], kb_model.distortions[1], 
             kb_model.distortions[2], kb_model.distortions[3]);

    // Generate sample points using the KB model
    let n_points = 500;
    let (points_2d, points_3d) = geometry::sample_points(Some(&kb_model), n_points)?;
    println!("\nGenerated {} point correspondences for optimization", points_2d.ncols());

    // Create initial Double Sphere model with reasonable starting values
    let initial_ds_model = DoubleSphereModel {
        intrinsics: kb_model.intrinsics.clone(),
        resolution: kb_model.resolution.clone(),
        alpha: 0.5,  // Valid initial value in range (0, 1]
        xi: 0.1,     // Small positive initial value
    };

    println!("\nInitial Double Sphere Model:");
    println!("  Intrinsics: fx={:.6}, fy={:.6}, cx={:.6}, cy={:.6}", 
             initial_ds_model.intrinsics.fx, initial_ds_model.intrinsics.fy, 
             initial_ds_model.intrinsics.cx, initial_ds_model.intrinsics.cy);
    println!("  Alpha: {:.6}, Xi: {:.6}", initial_ds_model.alpha, initial_ds_model.xi);

    // Create optimization cost function
    let mut ds_optimizer = DoubleSphereOptimizationCost::new(
        initial_ds_model, 
        points_3d, 
        points_2d
    );

    // Perform linear estimation
    println!("\nPerforming linear estimation...");
    ds_optimizer.linear_estimation()?;

    println!("Linear estimation results:");
    let distortion = ds_optimizer.get_distortion();
    println!("  Alpha: {:.6}, Xi: {:.6}", distortion[0], distortion[1]);

    // Perform non-linear optimization
    println!("\nPerforming non-linear optimization...");
    match ds_optimizer.optimize(true) {
        Ok(()) => {
            println!("Optimization completed successfully!");
        }
        Err(e) => {
            println!("Optimization failed: {:?}", e);
            println!("Continuing with linear estimation results...");
        }
    }

    // Get final optimized model parameters
    let final_intrinsics = ds_optimizer.get_intrinsics();
    let final_resolution = ds_optimizer.get_resolution();
    let final_distortion = ds_optimizer.get_distortion();

    println!("\n=== FINAL CONVERSION RESULTS ===");
    println!("Optimized Double Sphere Model:");
    println!("  Intrinsics: fx={:.6}, fy={:.6}, cx={:.6}, cy={:.6}",
             final_intrinsics.fx, final_intrinsics.fy,
             final_intrinsics.cx, final_intrinsics.cy);
    println!("  Alpha: {:.10}, Xi: {:.10}", final_distortion[0], final_distortion[1]);
    println!("  Resolution: {}x{}", final_resolution.width, final_resolution.height);

    // Create a DoubleSphereModel from the optimized parameters for saving
    let final_ds_model = DoubleSphereModel {
        intrinsics: final_intrinsics,
        resolution: final_resolution,
        alpha: final_distortion[0],
        xi: final_distortion[1],
    };

    // Save the result to YAML
    let output_path = "output/kb_to_ds_converted.yaml";
    final_ds_model.save_to_yaml(output_path)?;
    println!("\nSaved converted model to: {}", output_path);

    // Compare a few projection results
    println!("\n=== PROJECTION COMPARISON ===");
    let test_points_3d = vec![
        nalgebra::Vector3::new(0.0, 0.0, 1.0),
        nalgebra::Vector3::new(0.1, 0.1, 1.0),
        nalgebra::Vector3::new(0.2, 0.0, 1.0),
        nalgebra::Vector3::new(0.0, 0.2, 1.0),
        nalgebra::Vector3::new(-0.1, -0.1, 1.0),
    ];

    for (i, point_3d) in test_points_3d.iter().enumerate() {
        match (kb_model.project(point_3d), final_ds_model.project(point_3d)) {
            (Ok(kb_proj), Ok(ds_proj)) => {
                let diff_x = kb_proj.x - ds_proj.x;
                let diff_y = kb_proj.y - ds_proj.y;
                let diff_norm = (diff_x * diff_x + diff_y * diff_y).sqrt();
                println!("Point {}: KB({:.2}, {:.2}) -> DS({:.2}, {:.2}) | Diff: {:.4} px", 
                         i, kb_proj.x, kb_proj.y, ds_proj.x, ds_proj.y, diff_norm);
            }
            _ => println!("Point {}: Projection failed", i),
        }
    }

    println!("\n=== CONVERSION COMPLETE ===");
    Ok(())
}
