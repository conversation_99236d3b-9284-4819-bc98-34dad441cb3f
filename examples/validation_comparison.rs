//! Validation and Comparison Tool
//!
//! This example validates the Rust implementation against expected results
//! and provides detailed analysis of the Kannala-Brandt to Double Sphere conversion.
//! It's designed to help debug differences between Rust (Factor) and C++ (Ceres) implementations.
//!
//! Usage:
//! ```bash
//! cargo run --example validation_comparison
//! ```

use fisheye_tools::camera::{CameraModel, DoubleSphereModel, KannalaBrandtModel};
use fisheye_tools::optimization::{DoubleSphereOptimizationCost, Optimizer};
use fisheye_tools::geometry;
use nalgebra::Vector3;
use std::fs;

fn main() -> Result<(), Box<dyn std::error::Error>> {
    env_logger::init();

    println!("=== FISHEYE CAMERA MODEL CONVERSION VALIDATION ===");
    println!("Comparing Rust (Factor) vs Expected C++ (Ceres) Results\n");
    
    // Load <PERSON> model from YAML
    let kb_model = KannalaBrandtModel::load_from_yaml("samples/kannala_brandt.yaml")?;
    
    println!("📷 Input Kannala-Brandt Model:");
    print_kb_model(&kb_model);

    // Test different optimization strategies
    println!("\n🔬 TESTING DIFFERENT OPTIMIZATION APPROACHES:");
    
    // Strategy 1: Conservative point sampling
    test_conversion_strategy(&kb_model, 100, "Conservative (100 points)")?;
    
    // Strategy 2: Standard point sampling  
    test_conversion_strategy(&kb_model, 500, "Standard (500 points)")?;
    
    // Strategy 3: Dense point sampling
    test_conversion_strategy(&kb_model, 1000, "Dense (1000 points)")?;

    // Test projection accuracy
    println!("\n🎯 PROJECTION ACCURACY ANALYSIS:");
    test_projection_accuracy(&kb_model)?;

    // Test edge cases
    println!("\n⚠️  EDGE CASE TESTING:");
    test_edge_cases(&kb_model)?;

    // Generate detailed report
    println!("\n📊 GENERATING DETAILED ANALYSIS REPORT:");
    generate_analysis_report(&kb_model)?;

    println!("\n✅ VALIDATION COMPLETE");
    println!("Check output/validation_report.txt for detailed analysis");
    
    Ok(())
}

fn print_kb_model(model: &KannalaBrandtModel) {
    println!("  Intrinsics: fx={:.6}, fy={:.6}, cx={:.6}, cy={:.6}", 
             model.intrinsics.fx, model.intrinsics.fy, 
             model.intrinsics.cx, model.intrinsics.cy);
    println!("  Resolution: {}x{}", model.resolution.width, model.resolution.height);
    println!("  Distortion: k1={:.10}, k2={:.10}, k3={:.10}, k4={:.10}", 
             model.distortions[0], model.distortions[1], 
             model.distortions[2], model.distortions[3]);
}

fn test_conversion_strategy(
    kb_model: &KannalaBrandtModel, 
    n_points: usize, 
    strategy_name: &str
) -> Result<(), Box<dyn std::error::Error>> {
    println!("\n  🧪 Testing: {}", strategy_name);
    
    // Generate sample points
    let (points_2d, points_3d) = geometry::sample_points(Some(kb_model), n_points)?;
    
    // Create initial DS model
    let initial_ds_model = DoubleSphereModel {
        intrinsics: kb_model.intrinsics.clone(),
        resolution: kb_model.resolution.clone(),
        alpha: 0.5,
        xi: 0.1,
    };

    // Create optimizer
    let mut ds_optimizer = DoubleSphereOptimizationCost::new(
        initial_ds_model, 
        points_3d, 
        points_2d
    );

    // Linear estimation
    ds_optimizer.linear_estimation()?;
    let linear_distortion = ds_optimizer.get_distortion();
    
    // Non-linear optimization
    match ds_optimizer.optimize(false) {
        Ok(()) => {
            let final_intrinsics = ds_optimizer.get_intrinsics();
            let final_distortion = ds_optimizer.get_distortion();
            
            println!("    Linear:  α={:.6}, ξ={:.6}", linear_distortion[0], linear_distortion[1]);
            println!("    Final:   α={:.6}, ξ={:.6}", final_distortion[0], final_distortion[1]);
            println!("    Intrinsics: fx={:.2}, fy={:.2}, cx={:.2}, cy={:.2}", 
                     final_intrinsics.fx, final_intrinsics.fy, 
                     final_intrinsics.cx, final_intrinsics.cy);
        }
        Err(e) => {
            println!("    ❌ Optimization failed: {:?}", e);
        }
    }
    
    Ok(())
}

fn test_projection_accuracy(kb_model: &KannalaBrandtModel) -> Result<(), Box<dyn std::error::Error>> {
    // Convert to DS model first
    let (points_2d, points_3d) = geometry::sample_points(Some(kb_model), 500)?;
    
    let initial_ds_model = DoubleSphereModel {
        intrinsics: kb_model.intrinsics.clone(),
        resolution: kb_model.resolution.clone(),
        alpha: 0.5,
        xi: 0.1,
    };

    let mut ds_optimizer = DoubleSphereOptimizationCost::new(
        initial_ds_model, 
        points_3d, 
        points_2d
    );

    ds_optimizer.linear_estimation()?;
    ds_optimizer.optimize(false)?;

    // Create final DS model
    let final_ds_model = DoubleSphereModel {
        intrinsics: ds_optimizer.get_intrinsics(),
        resolution: ds_optimizer.get_resolution(),
        alpha: ds_optimizer.get_distortion()[0],
        xi: ds_optimizer.get_distortion()[1],
    };

    // Test projection accuracy at different regions
    let test_regions = vec![
        ("Center", Vector3::new(0.0, 0.0, 1.0)),
        ("Near Center", Vector3::new(0.05, 0.05, 1.0)),
        ("Mid Region", Vector3::new(0.15, 0.1, 1.0)),
        ("Edge Region", Vector3::new(0.3, 0.2, 1.0)),
        ("Far Edge", Vector3::new(0.4, 0.3, 1.0)),
    ];

    let mut total_error = 0.0f64;
    let mut max_error = 0.0f64;
    let mut valid_projections = 0;

    for (region_name, point_3d) in test_regions {
        match (kb_model.project(&point_3d), final_ds_model.project(&point_3d)) {
            (Ok(kb_proj), Ok(ds_proj)) => {
                let error = ((kb_proj.x - ds_proj.x).powi(2) + (kb_proj.y - ds_proj.y).powi(2)).sqrt();
                total_error += error;
                max_error = max_error.max(error);
                valid_projections += 1;
                
                println!("  {}: KB({:.2}, {:.2}) → DS({:.2}, {:.2}) | Error: {:.4} px", 
                         region_name, kb_proj.x, kb_proj.y, ds_proj.x, ds_proj.y, error);
            }
            _ => {
                println!("  {}: Projection failed", region_name);
            }
        }
    }

    if valid_projections > 0 {
        let avg_error = total_error / valid_projections as f64;
        println!("  📈 Average Error: {:.4} px, Max Error: {:.4} px", avg_error, max_error);
    }

    Ok(())
}

fn test_edge_cases(kb_model: &KannalaBrandtModel) -> Result<(), Box<dyn std::error::Error>> {
    println!("  Testing extreme 3D points...");
    
    let edge_cases = vec![
        ("Very close Z", Vector3::new(0.1, 0.1, 0.1)),
        ("Far Z", Vector3::new(0.1, 0.1, 10.0)),
        ("Large X", Vector3::new(2.0, 0.1, 1.0)),
        ("Large Y", Vector3::new(0.1, 2.0, 1.0)),
        ("Negative coords", Vector3::new(-0.5, -0.3, 1.0)),
    ];

    for (case_name, point_3d) in edge_cases {
        match kb_model.project(&point_3d) {
            Ok(proj) => {
                println!("    {}: ({:.2}, {:.2}) ✓", case_name, proj.x, proj.y);
            }
            Err(_) => {
                println!("    {}: Projection failed ❌", case_name);
            }
        }
    }

    Ok(())
}

fn generate_analysis_report(kb_model: &KannalaBrandtModel) -> Result<(), Box<dyn std::error::Error>> {
    let mut report = String::new();
    
    report.push_str("FISHEYE CAMERA MODEL CONVERSION ANALYSIS REPORT\n");
    report.push_str("==============================================\n\n");
    
    report.push_str("INPUT KANNALA-BRANDT MODEL:\n");
    report.push_str(&format!("  fx: {:.10}\n", kb_model.intrinsics.fx));
    report.push_str(&format!("  fy: {:.10}\n", kb_model.intrinsics.fy));
    report.push_str(&format!("  cx: {:.10}\n", kb_model.intrinsics.cx));
    report.push_str(&format!("  cy: {:.10}\n", kb_model.intrinsics.cy));
    report.push_str(&format!("  k1: {:.15}\n", kb_model.distortions[0]));
    report.push_str(&format!("  k2: {:.15}\n", kb_model.distortions[1]));
    report.push_str(&format!("  k3: {:.15}\n", kb_model.distortions[2]));
    report.push_str(&format!("  k4: {:.15}\n", kb_model.distortions[3]));
    report.push_str(&format!("  Resolution: {}x{}\n\n", kb_model.resolution.width, kb_model.resolution.height));

    // Perform conversion for report
    let (points_2d, points_3d) = geometry::sample_points(Some(kb_model), 500)?;
    
    let initial_ds_model = DoubleSphereModel {
        intrinsics: kb_model.intrinsics.clone(),
        resolution: kb_model.resolution.clone(),
        alpha: 0.5,
        xi: 0.1,
    };

    let mut ds_optimizer = DoubleSphereOptimizationCost::new(
        initial_ds_model, 
        points_3d, 
        points_2d
    );

    ds_optimizer.linear_estimation()?;
    let linear_distortion = ds_optimizer.get_distortion();
    
    ds_optimizer.optimize(false)?;
    let final_intrinsics = ds_optimizer.get_intrinsics();
    let final_distortion = ds_optimizer.get_distortion();

    report.push_str("CONVERSION RESULTS:\n");
    report.push_str("Linear Estimation:\n");
    report.push_str(&format!("  alpha: {:.15}\n", linear_distortion[0]));
    report.push_str(&format!("  xi: {:.15}\n", linear_distortion[1]));
    report.push_str("\nFinal Optimization:\n");
    report.push_str(&format!("  fx: {:.10}\n", final_intrinsics.fx));
    report.push_str(&format!("  fy: {:.10}\n", final_intrinsics.fy));
    report.push_str(&format!("  cx: {:.10}\n", final_intrinsics.cx));
    report.push_str(&format!("  cy: {:.10}\n", final_intrinsics.cy));
    report.push_str(&format!("  alpha: {:.15}\n", final_distortion[0]));
    report.push_str(&format!("  xi: {:.15}\n", final_distortion[1]));

    report.push_str("\nOPTIMIZATION FRAMEWORK:\n");
    report.push_str("  Rust Implementation: Factor library\n");
    report.push_str("  C++ Reference: Ceres Solver\n");
    report.push_str("  Algorithm: Levenberg-Marquardt\n");

    // Save report
    fs::write("output/validation_report.txt", report)?;
    
    Ok(())
}
