//! Mathematical Validation of Camera Models
//!
//! This example validates the mathematical correctness of the camera model implementations
//! by testing projection/unprojection consistency and Jacobian accuracy.
//!
//! Usage:
//! ```bash
//! cargo run --example mathematical_validation
//! ```

use fisheye_tools::camera::{CameraModel, DoubleSphereModel, KannalaBrandtModel};
use fisheye_tools::optimization::{DoubleSphereOptimizationCost, KannalaBrandtOptimizationCost, Optimizer};
use nalgebra::Vector3;

fn main() -> Result<(), Box<dyn std::error::Error>> {
    env_logger::init();

    println!("=== MATHEMATICAL VALIDATION OF CAMERA MODELS ===\n");

    // Test Kannala-Brandt model
    println!("🔍 KANNALA-BRANDT MODEL VALIDATION:");
    test_kannala_brandt_math()?;

    // Test Double Sphere model  
    println!("\n🔍 DOUBLE SPHERE MODEL VALIDATION:");
    test_double_sphere_math()?;

    // Test conversion consistency
    println!("\n🔍 CONVERSION CONSISTENCY VALIDATION:");
    test_conversion_consistency()?;

    // Test Jacobian accuracy
    println!("\n🔍 JACOBIAN ACCURACY VALIDATION:");
    test_jacobian_accuracy()?;

    println!("\n✅ MATHEMATICAL VALIDATION COMPLETE");
    Ok(())
}

fn test_kannala_brandt_math() -> Result<(), Box<dyn std::error::Error>> {
    let kb_model = KannalaBrandtModel::load_from_yaml("samples/kannala_brandt.yaml")?;
    
    println!("  Testing projection/unprojection consistency...");
    
    let test_points_3d = vec![
        Vector3::new(0.0, 0.0, 1.0),
        Vector3::new(0.1, 0.1, 1.0),
        Vector3::new(0.2, 0.0, 1.0),
        Vector3::new(0.0, 0.2, 1.0),
        Vector3::new(-0.1, -0.1, 1.0),
        Vector3::new(0.3, 0.2, 1.0),
    ];

    let mut total_error = 0.0f64;
    let mut valid_tests = 0;

    for (i, point_3d) in test_points_3d.iter().enumerate() {
        match kb_model.project(point_3d) {
            Ok(point_2d) => {
                match kb_model.unproject(&point_2d) {
                    Ok(unprojected_3d) => {
                        // Normalize both rays for comparison
                        let original_norm = point_3d.normalize();
                        let unprojected_norm = unprojected_3d.normalize();
                        
                        let error = (original_norm - unprojected_norm).norm();
                        total_error += error;
                        valid_tests += 1;
                        
                        println!("    Point {}: Error = {:.6}", i, error);
                    }
                    Err(_) => println!("    Point {}: Unprojection failed", i),
                }
            }
            Err(_) => println!("    Point {}: Projection failed", i),
        }
    }

    if valid_tests > 0 {
        let avg_error = total_error / valid_tests as f64;
        println!("    Average projection/unprojection error: {:.8}", avg_error);
        
        if avg_error < 1e-6 {
            println!("    ✅ KB projection/unprojection consistency: EXCELLENT");
        } else if avg_error < 1e-4 {
            println!("    ✅ KB projection/unprojection consistency: GOOD");
        } else {
            println!("    ⚠️  KB projection/unprojection consistency: NEEDS IMPROVEMENT");
        }
    }

    Ok(())
}

fn test_double_sphere_math() -> Result<(), Box<dyn std::error::Error>> {
    let ds_model = DoubleSphereModel::load_from_yaml("samples/double_sphere.yaml")?;
    
    println!("  Testing projection/unprojection consistency...");
    
    let test_points_3d = vec![
        Vector3::new(0.0, 0.0, 1.0),
        Vector3::new(0.1, 0.1, 1.0),
        Vector3::new(0.2, 0.0, 1.0),
        Vector3::new(0.0, 0.2, 1.0),
        Vector3::new(-0.1, -0.1, 1.0),
        Vector3::new(0.3, 0.2, 1.0),
    ];

    let mut total_error = 0.0f64;
    let mut valid_tests = 0;

    for (i, point_3d) in test_points_3d.iter().enumerate() {
        match ds_model.project(point_3d) {
            Ok(point_2d) => {
                match ds_model.unproject(&point_2d) {
                    Ok(unprojected_3d) => {
                        // Normalize both rays for comparison
                        let original_norm = point_3d.normalize();
                        let unprojected_norm = unprojected_3d.normalize();
                        
                        let error = (original_norm - unprojected_norm).norm();
                        total_error += error;
                        valid_tests += 1;
                        
                        println!("    Point {}: Error = {:.6}", i, error);
                    }
                    Err(_) => println!("    Point {}: Unprojection failed", i),
                }
            }
            Err(_) => println!("    Point {}: Projection failed", i),
        }
    }

    if valid_tests > 0 {
        let avg_error = total_error / valid_tests as f64;
        println!("    Average projection/unprojection error: {:.8}", avg_error);
        
        if avg_error < 1e-6 {
            println!("    ✅ DS projection/unprojection consistency: EXCELLENT");
        } else if avg_error < 1e-4 {
            println!("    ✅ DS projection/unprojection consistency: GOOD");
        } else {
            println!("    ⚠️  DS projection/unprojection consistency: NEEDS IMPROVEMENT");
        }
    }

    Ok(())
}

fn test_conversion_consistency() -> Result<(), Box<dyn std::error::Error>> {
    let kb_model = KannalaBrandtModel::load_from_yaml("samples/kannala_brandt.yaml")?;
    
    println!("  Testing conversion consistency across different point sets...");
    
    let point_counts = vec![100, 300, 500, 1000];
    let mut results = Vec::new();

    for &n_points in &point_counts {
        let (points_2d, points_3d) = fisheye_tools::geometry::sample_points(Some(&kb_model), n_points)?;
        
        let initial_ds_model = DoubleSphereModel {
            intrinsics: kb_model.intrinsics.clone(),
            resolution: kb_model.resolution.clone(),
            alpha: 0.5,
            xi: 0.1,
        };

        let mut ds_optimizer = DoubleSphereOptimizationCost::new(
            initial_ds_model, 
            points_3d, 
            points_2d
        );

        ds_optimizer.linear_estimation()?;
        ds_optimizer.optimize(false)?;

        let final_distortion = ds_optimizer.get_distortion();
        results.push((n_points, final_distortion[0], final_distortion[1]));
        
        println!("    {} points: α={:.6}, ξ={:.6}", n_points, final_distortion[0], final_distortion[1]);
    }

    // Check consistency
    let alpha_values: Vec<f64> = results.iter().map(|(_, alpha, _)| *alpha).collect();
    let xi_values: Vec<f64> = results.iter().map(|(_, _, xi)| *xi).collect();
    
    let alpha_std = calculate_std(&alpha_values);
    let xi_std = calculate_std(&xi_values);
    
    println!("    Alpha standard deviation: {:.6}", alpha_std);
    println!("    Xi standard deviation: {:.6}", xi_std);
    
    if alpha_std < 0.01 && xi_std < 0.01 {
        println!("    ✅ Conversion consistency: EXCELLENT");
    } else if alpha_std < 0.05 && xi_std < 0.05 {
        println!("    ✅ Conversion consistency: GOOD");
    } else {
        println!("    ⚠️  Conversion consistency: NEEDS IMPROVEMENT");
    }

    Ok(())
}

fn test_jacobian_accuracy() -> Result<(), Box<dyn std::error::Error>> {
    println!("  Testing analytical Jacobian accuracy...");
    
    // Test KB Jacobian
    let kb_model = KannalaBrandtModel::load_from_yaml("samples/kannala_brandt.yaml")?;
    let (points_2d, points_3d) = fisheye_tools::geometry::sample_points(Some(&kb_model), 10)?;
    
    let kb_optimizer = KannalaBrandtOptimizationCost::new(
        kb_model.clone(),
        points_3d.clone(),
        points_2d.clone(),
    );
    
    match kb_optimizer.validate_jacobian(1e-6) {
        Ok(is_valid) => {
            if is_valid {
                println!("    ✅ KB Jacobian validation: PASSED");
            } else {
                println!("    ⚠️  KB Jacobian validation: FAILED");
            }
        }
        Err(e) => {
            println!("    ❌ KB Jacobian validation error: {:?}", e);
        }
    }

    // Test DS Jacobian
    let ds_model = DoubleSphereModel::load_from_yaml("samples/double_sphere.yaml")?;
    let (points_2d_ds, points_3d_ds) = fisheye_tools::geometry::sample_points(Some(&ds_model), 10)?;
    
    let ds_optimizer = DoubleSphereOptimizationCost::new(
        ds_model.clone(),
        points_3d_ds.clone(),
        points_2d_ds.clone(),
    );
    
    match ds_optimizer.validate_jacobian(1e-6) {
        Ok(is_valid) => {
            if is_valid {
                println!("    ✅ DS Jacobian validation: PASSED");
            } else {
                println!("    ⚠️  DS Jacobian validation: FAILED");
            }
        }
        Err(e) => {
            println!("    ❌ DS Jacobian validation error: {:?}", e);
        }
    }

    Ok(())
}

fn calculate_std(values: &[f64]) -> f64 {
    if values.is_empty() {
        return 0.0;
    }
    
    let mean = values.iter().sum::<f64>() / values.len() as f64;
    let variance = values.iter()
        .map(|x| (x - mean).powi(2))
        .sum::<f64>() / values.len() as f64;
    
    variance.sqrt()
}
